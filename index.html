<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IdéaLab - Plateforme Collaborative d'Idées</title>
    <meta name="description" content="Plateforme collaborative pour partager et développer des idées innovantes adaptées au contexte africain">
    <meta name="keywords" content="idées, innovation, Afrique, collaboration, plateforme">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="icon" type="image/png" href="/favicon.png">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://my-idea-platform.com/">
    <meta property="og:title" content="IdéaLab - Plateforme Collaborative d'Idées">
    <meta property="og:description" content="Partagez vos idées innovantes et contribuez au développement de solutions adaptées au contexte africain">
    <meta property="og:image" content="/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://my-idea-platform.com/">
    <meta property="twitter:title" content="IdéaLab - Plateforme Collaborative d'Idées">
    <meta property="twitter:description" content="Partagez vos idées innovantes et contribuez au développement de solutions adaptées au contexte africain">
    <meta property="twitter:image" content="/twitter-image.png">
    
    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    
    <style>
        /* Loading screen styles */
        #loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
            font-family: 'Inter', sans-serif;
        }
        
        .loading-logo {
            font-size: 3rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        
        .loading-text {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Hide loading screen when app is ready */
        .app-loaded #loading-screen {
            display: none;
        }
        
        /* Base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        /* App container */
        #app {
            min-height: 100vh;
        }
        
        /* Utility classes */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen">
        <div class="loading-logo"><i class="fas fa-lightbulb"></i></div>
        <div class="loading-text">
            <h1>IdéaLab</h1>
            <p>Chargement de votre plateforme d'idées...</p>
        </div>
        <div class="loading-spinner"></div>
    </div>
    
    <!-- Main App Container -->
    <div id="app">
        <!-- Vue.js app will be mounted here -->
        <noscript>
            <div style="text-align: center; padding: 2rem; background: #fff3cd; color: #856404; margin: 2rem;">
                <h2>JavaScript requis</h2>
                <p>Cette application nécessite JavaScript pour fonctionner correctement.</p>
                <p>Veuillez activer JavaScript dans votre navigateur et recharger la page.</p>
            </div>
        </noscript>
    </div>
    
    <!-- Vite will inject the script tag here -->
    <script type="module" src="/src/main.js"></script>
    
    <script>
        // Hide loading screen when app is ready
        window.addEventListener('load', function() {
            setTimeout(function() {
                document.body.classList.add('app-loaded');
            }, 1000); // Small delay to show loading animation
        });
        
        // Error handling for failed script loading
        window.addEventListener('error', function(e) {
            if (e.target.tagName === 'SCRIPT') {
                document.getElementById('loading-screen').innerHTML = `
                    <div style="text-align: center; color: white;">
                        <h2>❌ Erreur de chargement</h2>
                        <p>Impossible de charger l'application.</p>
                        <p>Veuillez vérifier votre connexion internet et recharger la page.</p>
                        <button onclick="location.reload()" style="
                            margin-top: 1rem; 
                            padding: 0.5rem 1rem; 
                            background: white; 
                            color: #333; 
                            border: none; 
                            border-radius: 4px; 
                            cursor: pointer;
                        ">
                            Recharger la page
                        </button>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
