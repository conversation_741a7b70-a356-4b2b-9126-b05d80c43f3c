# 🌐 Configuration de production pour IdéaLab
# ============================================

# Configuration frontend (Vite) - Variables VITE_*
# URL de l'API pour la production (Vercel)
VITE_API_URL=https://idealab-six.vercel.app/api

# Configuration de l'application frontend
VITE_APP_NAME=IdéaLab
VITE_APP_VERSION=1.0.0

# Configuration de la base de données
# Remplacez par votre URL de base de données PostgreSQL de production
DATABASE_URL=postgresql://username:password@host:port/idealab_production

# Configuration JWT
# Générez une clé secrète forte pour la production
JWT_SECRET=your-super-secret-jwt-key-for-production-change-this

# Configuration de l'application
PORT=3000

# Configuration CORS
# Remplacez par votre domaine Vercel
CORS_ORIGIN=https://your-idealab-app.vercel.app

# Configuration de la base de données (optionnel si DATABASE_URL est défini)
DB_HOST=your-db-host
DB_PORT=5432
DB_NAME=idealab_production
DB_USER=your-db-user
DB_PASSWORD=your-db-password

# Configuration de sécurité
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key

# Configuration des uploads (si applicable)
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif

# Configuration des emails (si applicable)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Configuration de monitoring (optionnel)
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info

# Configuration de cache (optionnel)
REDIS_URL=redis://your-redis-url

# Limites de taux (rate limiting)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
